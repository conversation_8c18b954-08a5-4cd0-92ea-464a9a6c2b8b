export type CustomAlertState = {
  visible: boolean;
  title?: string;
  message: string;
  buttonText?: string;
}

export type CustomAlertProps = {
  visible: boolean;
  title?: string;
  message: string;
  buttonText?: string;
  onClose: () => void;
}

export const initialAlertState: CustomAlertState = {
  visible: false,
  title: undefined,
  message: '',
  buttonText: 'OK',
};

export const showAlert = (
  setAlertState: (state: CustomAlertState) => void,
  message: string,
  title?: string,
  buttonText?: string
) => {
  setAlertState({
    visible: true,
    title,
    message,
    buttonText: buttonText || 'OK',
  });
};

export const hideAlert = (setAlertState: (state: CustomAlertState) => void) => {
  setAlertState(initialAlertState);
};
