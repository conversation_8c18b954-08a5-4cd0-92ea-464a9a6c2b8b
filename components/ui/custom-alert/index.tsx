import React from 'react';
import { Modal, View, Text, Pressable } from 'react-native';
import AlertIcon from '@/components/icons/Alert';
import { COLOURS } from '@/constants/colours';
import styles from './styles';
import { CustomAlertProps } from './services';

const CustomAlert: React.FC<CustomAlertProps> = ({
  visible,
  title,
  message,
  buttonText = 'OK',
  onClose,
}) => {
  const {
    modalOverlay,
    modalContent,
    modalIcon,
    modalTitle,
    modalErrorText,
    modalButton,
    modalButtonText,
  } = styles;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={modalOverlay}>
        <View style={modalContent}>
          <AlertIcon width={56} height={56} style={modalIcon} color={COLOURS.errorText} />
          {title && <Text style={modalTitle}>{title}</Text>}
          <Text style={modalErrorText}>{message}</Text>
          <Pressable style={modalButton} onPress={onClose}>
            <Text style={modalButtonText}>{buttonText}</Text>
          </Pressable>
        </View>
      </View>
    </Modal>
  );
};

export default CustomAlert;
