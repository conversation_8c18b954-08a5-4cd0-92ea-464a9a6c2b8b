import {
  BarcodeScanningResult,
  CameraView,
  useCameraPermissions,
} from 'expo-camera';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Modal,
  Platform,
  StatusBar,
  TouchableOpacity,
  View,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { CloseIcon, TorchIcon } from '@/components/icons';
import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';

import styles from './styles';

interface QRScannerModalProps {
  visible: boolean;
  onClose: () => void;
  onScanSuccess: (data: string) => void;
}

export default function QRScannerModal({
  visible,
  onClose,
  onScanSuccess,
}: QRScannerModalProps) {
  const { t } = useTranslation();
  const [permission, requestPermission] = useCameraPermissions();
  const [scanned, setScanned] = useState(false);
  const [torch, setTorch] = useState(false);
  const insects = useSafeAreaInsets();

  useEffect(() => {
    if (visible) {
      requestPermission();
      setScanned(false);
    }
  }, [visible]);

  const handleBarCodeScanned = ({ type, data }: BarcodeScanningResult) => {
    if (!scanned) {
      setScanned(true);
      onScanSuccess(data);
      onClose();
    }
  };

  if (!permission) {
    return (
      <Modal
        visible={visible}
        animationType='slide'
        presentationStyle='fullScreen'
      >
        <View style={styles.center}>
          <TextTypes type='body' color={COLOURS.white}>
            {t('qr_scanner_requesting_permission')}
          </TextTypes>
        </View>
      </Modal>
    );
  }

  return (
    <Modal
      visible={visible}
      animationType='slide'
      presentationStyle='fullScreen'
      statusBarTranslucent={true}
    >
      <View
        key={permission.status}
        style={[styles.container, { paddingTop: insects.top }]}
      >
        <StatusBar
          barStyle='light-content'
          backgroundColor={COLOURS.qrBackground}
        />
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeBtn}>
            <CloseIcon color={COLOURS.white} />
          </TouchableOpacity>
          <View style={styles.closeBtn} />
        </View>
        <View style={styles.cameraWrapper}>
          <CameraView
            style={styles.camera}
            facing={'back'}
            enableTorch={torch}
            onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
            barcodeScannerSettings={{
              barcodeTypes: ['qr'],
            }}
            ratio={Platform.OS === 'ios' ? '16:9' : undefined}
          />
          {/* Scan Frame Overlay */}
          <View style={styles.scanFrameWrapper} pointerEvents='none'>
            <View style={styles.scanFrame}>
              {/* Corners */}
              <View style={[styles.corner, styles.topLeft]} />
              <View style={[styles.corner, styles.topRight]} />
              <View style={[styles.corner, styles.bottomLeft]} />
              <View style={[styles.corner, styles.bottomRight]} />
            </View>
          </View>
        </View>
        <View style={styles.bottomSection}>
          <View style={styles.flashBtn}>
            <TouchableOpacity
              style={styles.torchIcon}
              onPress={() => setTorch((prevTourchValue) => !prevTourchValue)}
            >
              <TorchIcon />
            </TouchableOpacity>
          </View>
          <View style={styles.instruction}>
            <TextTypes
              type='h3'
              color={COLOURS.textBlack}
              customStyle={styles.textAlign}
            >
              {t('qr_scanner_align_instruction')}
            </TextTypes>
          </View>
        </View>
      </View>
    </Modal>
  );
}
