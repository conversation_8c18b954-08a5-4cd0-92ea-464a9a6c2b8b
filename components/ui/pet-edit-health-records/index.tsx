import React from 'react';
import { TouchableOpacity, View } from 'react-native';

import { Add, Trash } from '@/components/icons';
import TextTypes from '@/components/text-types';
import ModalDatePicker from '@/components/ui/modal-date-picker';
import ModalDropdown from '@/components/ui/modal-dropdown';
import { COLOURS } from '@/constants/colours';

import {
  PetEditHealthRecordsProps,
  StatusIcon,
  getColor,
  getStatusStyle,
  getStatusText,
  treatmentTypes,
  vaccinationTypes,
} from './services';
import styles from './styles';

const PetEditHealthRecordsComponent: React.FC<PetEditHealthRecordsProps> = ({
  values,
  errors,
  touched,
  handleChange,
  handleBlur,
  setFieldValue,
  submitCount,
  vaccinations,
  setVaccinations,
  treatments,
  setTreatments,
}) => {
  const {
    section,
    sectionHeader,
    sectionTitle,
    addButton,
    addButtonText,
    vaccinationItem,
    vaccinationRow,
    statusRow,
    statusBadge,
    deleteButton,
    treatmentItem,
    noMargin,
    marginH20,
    disableTrash,
  } = styles;

  // Helper functions for vaccinations
  const addVaccination = () => {
    const newId = Math.max(...vaccinations.map((v) => v.id), 0) + 1;
    setVaccinations([
      ...vaccinations,
      {
        id: newId,
        name: '',
        date: new Date().toISOString().split('T')[0],
        status: 'up-to-date' as const,
      },
    ]);
  };

  const deleteVaccination = (id: number) => {
    setVaccinations(vaccinations.filter((v) => v.id !== id));
  };

  const updateVaccination = (id: number, field: string, value: any) => {
    setVaccinations(
      vaccinations.map((v) => (v.id === id ? { ...v, [field]: value } : v))
    );
  };

  // Helper functions for treatments
  const addTreatment = () => {
    const newId = Math.max(...treatments.map((t) => t.id), 0) + 1;
    setTreatments([
      ...treatments,
      {
        id: newId,
        name: '',
        date: null,
        status: 'up-to-date' as const,
      },
    ]);
  };

  const deleteTreatment = (id: number) => {
    setTreatments(treatments.filter((t) => t.id !== id));
  };

  const updateTreatment = (id: number, field: string, value: any) => {
    setTreatments(
      treatments.map((t) => (t.id === id ? { ...t, [field]: value } : t))
    );
  };

  return (
    <>
      <View style={sectionHeader}>
        <TextTypes type='h4' color={COLOURS.primary} customStyle={sectionTitle}>
          Vaccinations
        </TextTypes>
        <TouchableOpacity style={addButton} onPress={addVaccination}>
          <Add width={24} height={24} color={COLOURS.white} />
          <TextTypes
            type='h5'
            color={COLOURS.white}
            customStyle={addButtonText}
          >
            ADD
          </TextTypes>
        </TouchableOpacity>
      </View>
      {/* Vaccinations Section */}
      {vaccinations.length > 0 && (
        <View style={section}>
          {vaccinations.map((vaccination, index) => (
            <View
              key={vaccination.id}
              style={[
                vaccinationItem,
                index === vaccinations.length - 1 && noMargin,
              ]}
            >
              <View style={marginH20}>
                <ModalDropdown
                  label='Vaccination'
                  placeholder='Select vaccination'
                  value={vaccinationTypes.find(
                    (type) => type.value === vaccination.name
                  )}
                  onChange={(item) => {
                    updateVaccination(vaccination.id, 'name', item.value);
                  }}
                  options={vaccinationTypes}
                />

                <ModalDatePicker
                  label='Date given'
                  value={
                    vaccination.date ? new Date(vaccination.date) : undefined
                  }
                  onChange={(date) => {
                    updateVaccination(
                      vaccination.id,
                      'date',
                      date.toISOString().split('T')[0]
                    );
                  }}
                  placeholder='DD/MM/YYYY'
                />
                <View style={vaccinationRow}>
                  <View style={statusRow}>
                    <View
                      style={[statusBadge, getStatusStyle(vaccination.status)]}
                    >
                      {React.createElement(StatusIcon(vaccination.status))}
                      <TextTypes
                        type='errorText'
                        color={getColor(vaccination.status)}
                      >
                        {getStatusText(vaccination.status)}
                      </TextTypes>
                    </View>
                  </View>
                  <TouchableOpacity
                    style={[
                      deleteButton,
                      vaccinations.length === 1 && disableTrash,
                    ]}
                    disabled={vaccinations.length === 1}
                    onPress={() => deleteVaccination(vaccination.id)}
                  >
                    <Trash
                      width={20}
                      height={20}
                      color={
                        vaccinations.length === 1
                          ? COLOURS.grayIcon
                          : COLOURS.primary
                      }
                    />
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          ))}
        </View>
      )}
      <View style={sectionHeader}>
        <TextTypes type='h4' color={COLOURS.primary} customStyle={sectionTitle}>
          Treatments & preventatives
        </TextTypes>
        <TouchableOpacity style={addButton} onPress={addTreatment}>
          <Add width={24} height={24} color={COLOURS.white} />
          <TextTypes
            type='h5'
            color={COLOURS.white}
            customStyle={addButtonText}
          >
            ADD
          </TextTypes>
        </TouchableOpacity>
      </View>
      {/* Treatments & Preventatives Section */}
      {treatments.length > 0 && (
        <View style={section}>
          {treatments.map((treatment, index) => (
            <View
              key={treatment.id}
              style={[
                treatmentItem,
                index === treatments.length - 1 && noMargin,
              ]}
            >
              <View style={marginH20}>
                <ModalDropdown
                  label='Treatment'
                  placeholder='Select treatment'
                  value={treatmentTypes.find(
                    (type) => type.value === treatment.name
                  )}
                  onChange={(item) => {
                    updateTreatment(treatment.id, 'name', item.value);
                  }}
                  options={treatmentTypes}
                />

                <ModalDatePicker
                  label='Date given'
                  value={treatment.date ? new Date(treatment.date) : undefined}
                  onChange={(date) => {
                    updateTreatment(
                      treatment.id,
                      'date',
                      date.toISOString().split('T')[0]
                    );
                  }}
                  placeholder='DD/MM/YYYY'
                />
                <View style={vaccinationRow}>
                  <View style={statusRow}>
                    <View
                      style={[statusBadge, getStatusStyle(treatment.status)]}
                    >
                      {React.createElement(StatusIcon(treatment.status))}
                      <TextTypes
                        type='errorText'
                        color={getColor(treatment.status)}
                      >
                        {getStatusText(treatment.status)}
                      </TextTypes>
                    </View>
                  </View>

                  <TouchableOpacity
                    onPress={() => deleteTreatment(treatment.id)}
                    style={[
                      deleteButton,
                      treatments.length === 1 && disableTrash,
                    ]}
                    disabled={treatments.length === 1}
                  >
                    <Trash
                      width={20}
                      height={20}
                      color={
                        treatments.length === 1
                          ? COLOURS.grayIcon
                          : COLOURS.primary
                      }
                    />
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          ))}
        </View>
      )}
    </>
  );
};

export default PetEditHealthRecordsComponent;
