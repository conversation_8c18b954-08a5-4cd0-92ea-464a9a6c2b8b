import { COLOURS } from '@/constants/colours';

export type DropdownOption = {
  label: string;
  value: string;
}

export type ModalDropdownProps = {
  value?: DropdownOption;
  onChange: (option: DropdownOption) => void;
  label?: string;
  placeholder?: string;
  options: DropdownOption[];
  disabled?: boolean;
  customStyle?: any;
  inline?: boolean;
}

export const getDisplayText = (value?: DropdownOption, placeholder: string = 'Select option'): string => {
  if (value) {
    return value.label;
  }
  return placeholder;
};

export const getTextColor = (disabled: boolean, value?: DropdownOption): string => {
  if (disabled) {
    return COLOURS.youText;
  }
  return value ? COLOURS.textBlack : COLOURS.youText;
};

export const getChevronColor = (disabled: boolean): string => {
  return disabled ? COLOURS.youText : COLOURS.grayIcon;
};

export const getModalTitle = (label?: string): string => {
  return label || 'Select option';
};
