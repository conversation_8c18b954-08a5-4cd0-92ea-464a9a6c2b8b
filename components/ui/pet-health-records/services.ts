import { DueSoon, Overdue, UpToDate } from '@/components/icons';
import { COLOURS } from '@/constants/colours';

import styles from './styles';
import { PetDetailsProps } from '@/pet';

const { statusUpToDate, statusOverdue, statusDueSoon } = styles;

export type PetHealthRecordsComponentProps = {
  pet: PetDetailsProps;
}

export type VaccinationRecord = {
  name: string;
  status: 'up-to-date' | 'overdue' | 'due-soon';
}

export type TreatmentRecord = {
  name: string;
  date: string;
  type: string;
}

export type VetVisitRecord = {
  date: string;
  duration: string;
  title: string;
  reason: string;
}

export type ChatHistoryRecord = {
  date: string;
  title: string;
  time: string;
  duration?: string;
}

export const getColor = (status: string) => {
  switch (status) {
    case 'up-to-date':
      return COLOURS.primary;
    case 'overdue':
      return COLOURS.redText;
    case 'due-soon':
      return COLOURS.goldText;
    default:
      return COLOURS.primary;
  }
};

export const StatusIcon = (status: string) => {
  switch (status) {
    case 'up-to-date':
      return UpToDate;
    case 'overdue':
      return Overdue;
    case 'due-soon':
      return DueSoon;
    default:
      return UpToDate;
  }
};

export const getStatusStyle = (status: string) => {
  switch (status) {
    case 'up-to-date':
      return statusUpToDate;
    case 'overdue':
      return statusOverdue;
    case 'due-soon':
      return statusDueSoon;
    default:
      return statusUpToDate;
  }
};

export const getStatusText = (status: string) => {
  switch (status) {
    case 'up-to-date':
      return 'Up to date';
    case 'overdue':
      return 'Overdue';
    case 'due-soon':
      return 'Due soon';
    default:
      return 'Up to date';
  }
};

// Mock data - in real app this would come from props or API
export const vaccinations: VaccinationRecord[] = [
  { name: 'Rabies', status: 'up-to-date' },
  { name: 'Parvovirus', status: 'overdue' },
  { name: 'Kennel Cough', status: 'due-soon' },
];

export const vetVisits: VetVisitRecord[] = [
  {
    date: '17 Jun 2024',
    duration: '30 min',
    title: 'Video Consultation with Dr. Sarah',
    reason: `Arlo's chocolate toxicity`,
  },
];

export const chatHistory: ChatHistoryRecord[] = [
  {
    date: 'June 2024',
    title: 'Golden Retriever Grooming',
    time: '30 min ago',
  },
  {
    date: '',
    title: `Arlo's chocolate toxicity`,
    duration: '20 Min',
    time: '2 hours ago',
  },
  {
    date: '',
    title: 'Puppy Biting Behaviour',
    time: '1 week ago',
  },
];
