import { useLocalSearchParams, useRouter } from 'expo-router';
import { Formik } from 'formik';
import { useEffect, useState } from 'react';
import {
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import Header from '@/components/header';
import TextTypes from '@/components/text-types';
import Loader from '@/components/ui/Loader';
import PetEditDetailsComponent from '@/components/ui/pet-edit-details';
import PetEditHealthRecordsComponent from '@/components/ui/pet-edit-health-records';
import CustomAlert from '@/components/ui/custom-alert';
import { COLOURS } from '@/constants/colours';

import { EditPetFormData, fetchPetForEdit, initialTreatment, initialVaccination, updatePet, validationSchema, pickPetImage } from './services';
import { initialAlertState, showAlert, hideAlert, type CustomAlertState } from '@/components/ui/custom-alert/services';
import styles from './styles';
import { TreatmentRecord } from '@/components/ui/pet-edit-health-records/services';
import { useIsFocused } from '@react-navigation/native';
import { PetDetailsTabType } from '@/constants/string';

const EditPet = () => {
  const router = useRouter();
  const { id } = useLocalSearchParams();

  const [pet, setPet] = useState<EditPetFormData | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<PetDetailsTabType>(PetDetailsTabType.DETAILS);
  const [alertState, setAlertState] = useState<CustomAlertState>(initialAlertState);
  const isFocused = useIsFocused();

  // Health records state
  const [vaccinations, setVaccinations] = useState(initialVaccination);

  const [treatments, setTreatments] = useState<Array<TreatmentRecord>>(initialTreatment);

  const {
    container,
    contentContainer,
    petImage,
    tabs,
    tab,
    tabActive,
    buttonContainer,
    cancelButton,
    saveButton,
    cancelButtonText,
    saveButtonText,
    loaderStyle,
    mainContainer
  } = styles;

  useEffect(() => {
    const loadPetData = async () => {
      if (!id) return;
      if (!pet) {
        setLoading(true);
      }
      setError(null);
      try {
        const petData = await fetchPetForEdit(id as string);
        setPet(petData);
      } catch (err: any) {
        setError(err.message || 'Failed to load pet details');
      } finally {
        setLoading(false);
      }
    };

    loadPetData();
  }, [id, isFocused]);

  const handleSubmit = async (values: EditPetFormData) => {
    if (!id) return;

    setUpdating(true);
    try {
      await updatePet(id as string, values);
      onBackPress();
    } catch (err: any) {
      // Show custom alert for errors
      showAlert(setAlertState, err.message || 'Failed to update pet');
    } finally {
      setUpdating(false);
    }
  };

  const onBackPress = () => {
    router.replace({
      pathname: '/(tabs)/my-pets/pet-details',
      params: { id: String(id) },
    });
  }

  if (loading) {
    return (
      <SafeAreaView style={container}>
        <Header title='' isBack onBackPress={onBackPress} />
        <View style={loaderStyle}>
          <Loader />
        </View>
      </SafeAreaView>
    );
  }

  if (error || !pet) {
    return (
      <SafeAreaView style={container}>
        <Header title='' isBack onBackPress={onBackPress} />
        <View style={contentContainer}>
          <TextTypes type='body3' color='red'>
            {error || 'Pet not found'}
          </TextTypes>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={container}>
      <Header title='' isBack onBackPress={onBackPress} />
      <KeyboardAvoidingView
        style={mainContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <Formik
          initialValues={pet}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({
            values,
            errors,
            touched,
            handleChange,
            handleBlur,
            setFieldValue,
            handleSubmit: formikSubmit,
            submitCount,
          }) => {
            // Handler for picking an image
            const handlePickImage = async () => {
              await pickPetImage({
                onPicked: ({ uri, fileName, fileType }) => {
                  setFieldValue('image', { uri });
                  setFieldValue('imageUri', uri);
                  setFieldValue('imageFileName', fileName);
                  setFieldValue('imageFileType', fileType);
                },
                onError: (msg) => showAlert(setAlertState, msg),
              });
            };

            return (
              <>
                <ScrollView
                  contentContainerStyle={contentContainer}
                  showsVerticalScrollIndicator={false}
                >
                  {/* Pet Image - Clickable */}
                  <TouchableOpacity onPress={handlePickImage} activeOpacity={0.8}>
                    <Image source={values.image} style={petImage} />
                  </TouchableOpacity>

                {/* Tabs */}
                <View style={tabs}>
                  <TouchableOpacity
                    style={[tab, activeTab === PetDetailsTabType.DETAILS && tabActive]}
                    onPress={() => setActiveTab(PetDetailsTabType.DETAILS)}
                  >
                    <TextTypes type='h5' color={COLOURS.textBlack}>
                      Details
                    </TextTypes>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[tab, activeTab === PetDetailsTabType.HEALTH && tabActive]}
                    onPress={() => setActiveTab(PetDetailsTabType.HEALTH)}
                  >
                    <TextTypes type='h5' color={COLOURS.textBlack}>
                      Health records
                    </TextTypes>
                  </TouchableOpacity>
                </View>

                {/* Tab Content */}
                {activeTab === PetDetailsTabType.DETAILS ? (
                  <PetEditDetailsComponent
                    values={values}
                    errors={errors}
                    touched={touched}
                    handleChange={handleChange}
                    handleBlur={handleBlur}
                    setFieldValue={setFieldValue}
                    submitCount={submitCount}
                    petId={String(id)}
                  />
                ) : (
                  <PetEditHealthRecordsComponent
                    values={values}
                    errors={errors}
                    touched={touched}
                    handleChange={handleChange}
                    handleBlur={handleBlur}
                    setFieldValue={setFieldValue}
                    submitCount={submitCount}
                    vaccinations={vaccinations}
                    setVaccinations={setVaccinations}
                    treatments={treatments}
                    setTreatments={setTreatments}
                  />
                )}
              </ScrollView>

              {/* Bottom Buttons */}
              <View style={buttonContainer}>
                <TouchableOpacity
                  style={cancelButton}
                  onPress={() => router.back()}
                  disabled={updating}
                >
                  <TextTypes
                    type='buttonText'
                    color={COLOURS.primary}
                    customStyle={cancelButtonText}
                  >
                    CANCEL
                  </TextTypes>
                </TouchableOpacity>
                <TouchableOpacity
                  style={saveButton}
                  onPress={() => formikSubmit()}
                  disabled={updating}
                >
                  <TextTypes
                    type='buttonText'
                    color={COLOURS.white}
                    customStyle={saveButtonText}
                  >
                    {updating ? 'SAVING...' : 'SAVE'}
                  </TextTypes>
                </TouchableOpacity>
              </View>
            </>
          );
        }}
        </Formik>
      </KeyboardAvoidingView>

      <CustomAlert
        visible={alertState.visible}
        title={alertState.title}
        message={alertState.message}
        buttonText={alertState.buttonText}
        onClose={() => hideAlert(setAlertState)}
      />
    </SafeAreaView>
  );
};

export default EditPet;
