import * as Yup from 'yup';
import * as ImagePicker from 'expo-image-picker';

import { getS3SignedImageUrl, getS3SignedUrl } from '@/lib/vet247/documents';
import { uploadToS3 } from '@/lib/user/uploadUtils';
import { getPetDetails as fetchPetDetailsApi } from '@/lib/vet/getPetDetails';
import { updatePet as updatePetApi } from '@/lib/vet/updatePet';
import { verifyAccessToken } from '@/lib/vet/verifyAccessToken';
import { getUserId } from '@/lib/user/user';
import { getAccessToken } from '@/lib/auth/tokenManager';
import { TreatmentRecord } from '@/components/ui/pet-edit-health-records/services';
import { PetDetailsData } from '@/pet';

export type EditPetFormData = {
  name: string;
  species: string;
  breed: string;
  gender: string;
  weight: string;
  weightUnit: string;
  dateOfBirth: string | null;
  microchipNumber: string;
  insuranceProvider: string;
  insurancePolicyNumber: string;
  referralCode: string;
  image: any;
  imageUri?: string;
  imageFileName?: string;
  imageFileType?: string;
}

export type UpdatePetRequest = {
  name: string;
  species: string;
  breed: string;
  gender: string;
  weight: number;
  weightType: string;
  dateOfBirth: string | null;
  microchipNumber?: string;
  insuranceProvider?: string;
  insurancePolicyNumber?: string;
  vetPracticeId?: number;
  addedByReferral?: boolean;
  petPicture?: string;
}

export const validationSchema = Yup.object().shape({
  name: Yup.string().required('Pet name is required'),
  species: Yup.string().required('Species is required'),
  gender: Yup.string().required('Gender is required'),
  breed: Yup.string(),
  weight: Yup.string(),
  weightUnit: Yup.string(),
  dateOfBirth: Yup.string().nullable(),
  microchipNumber: Yup.string(),
  insuranceProvider: Yup.string(),
  insurancePolicyNumber: Yup.string(),
  referralCode: Yup.string(),
});

export async function fetchPetForEdit(
  petId: string | number
): Promise<EditPetFormData> {
  const petData = await fetchPetDetailsApi(petId);
  return mapPetDataToFormData(petData);
}

export async function mapPetDataToFormData(
  pet: PetDetailsData
): Promise<EditPetFormData> {
  let image: any = require('@/assets/images/logo-green--no-text.png');
  if (pet.petPicture) {
    try {
      image = { uri: await getS3SignedImageUrl(pet.petPicture) };
    } catch (e) {
      image = require('@/assets/images/icon.png');
    }
  }

  return {
    name: pet.name,
    species: pet.species,
    breed: pet.breed || '',
    gender: pet.gender,
    weight: pet.weight ? String(pet.weight) : '',
    weightUnit: pet.weightType || 'kg',
    dateOfBirth: pet.dateOfBirth || null,
    microchipNumber: pet.microchipNumber || '',
    insuranceProvider: pet.insuranceProvider || '',
    insurancePolicyNumber: pet.insurancePolicyNumber || '',
    referralCode: '',
    image,
    imageUri: undefined,
    imageFileName: undefined,
    imageFileType: undefined,
  };
}

export async function pickPetImage({
  onPicked,
  onError,
}: {
  onPicked: (file: { uri: string; fileName: string; fileType: string }) => void;
  onError?: (error: string) => void;
}) {
  const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
  if (status !== 'granted') {
    onError?.('Sorry, we need camera roll permissions to make this work!');
    return;
  }

  const result = await ImagePicker.launchImageLibraryAsync({
    mediaTypes: ['images'],
    allowsEditing: true,
    aspect: [1, 1],
    quality: 0.7,
  });

  if (!result.canceled && result.assets && result.assets.length > 0) {
    const asset = result.assets[0];
    const fileUri = asset.uri;
    const fileName = asset.fileName || fileUri.split('/').pop() || 'pet-picture.jpg';
    const fileType = asset.type || 'image/jpeg';
    onPicked({ uri: fileUri, fileName, fileType });
  }
}

export async function uploadPetImage(
  imageUri: string,
  imageFileName: string | undefined,
  imageFileType: string | undefined
): Promise<string> {
  const userId = await getUserId();
  const accessToken = await getAccessToken();

  if (!userId || !accessToken) {
    throw new Error('User authentication required');
  }

  const originalFileName = imageFileName || imageUri.split('/').pop() || 'pet-picture.jpg';
  const fileType = imageFileType || 'image/jpeg';
  const formattedFileName = `${userId}-pet_picture-${Date.now()}-${originalFileName}`;

  const signedUrl = await getS3SignedUrl(formattedFileName, fileType, accessToken);
  await uploadToS3(signedUrl, imageUri, fileType);

  return formattedFileName;
}

export async function updatePet(
  petId: string | number,
  formData: EditPetFormData
): Promise<void> {
  let vetPracticeId: number | undefined;
  let addedByReferral: boolean;
  let petPicture: string | undefined;

  // Handle referral code logic explicitly
  if (formData.referralCode && formData.referralCode.trim()) {
    // User has entered a referral code - validate it
    try {
      const accessCodeData = await verifyAccessToken(formData.referralCode.trim());
      if (accessCodeData && accessCodeData.id) {
        vetPracticeId = accessCodeData.id;
        addedByReferral = true;
      } else {
        // If API returns success but no valid practice ID, treat as invalid
        throw new Error('Code is invalid');
      }
    } catch (error) {
      // If user entered a code but it's invalid, show error and prevent update
      throw new Error('Code is invalid');
    }
  } else {
    // User has not entered a referral code (or cleared it) - explicitly reset values
    vetPracticeId = undefined;
    addedByReferral = false;
  }
  
  if (formData.imageUri && !formData.imageUri.startsWith('http')) {
    try {
      petPicture = await uploadPetImage(
        formData.imageUri,
        formData.imageFileName,
        formData.imageFileType
      );
    } catch (error) {
      throw new Error('Failed to upload pet image');
    }
  }

  const updateData: UpdatePetRequest = {
    name: formData.name,
    species: formData.species,
    breed: formData.breed,
    gender: formData.gender,
    weight: formData.weight ? parseFloat(formData.weight) : 0,
    weightType: formData.weightUnit,
    dateOfBirth: formData.dateOfBirth,
    microchipNumber: formData.microchipNumber || undefined,
    insuranceProvider: formData.insuranceProvider || undefined,
    insurancePolicyNumber: formData.insurancePolicyNumber || undefined,
    vetPracticeId,
    addedByReferral,
    petPicture,
  };

  await updatePetApi(petId, updateData);
}

export const initialVaccination = [
  { id: 1, name: 'Rabies', date: '2025-04-19', status: 'up-to-date' as const },
  { id: 2, name: 'Parvovirus', date: '2024-04-19', status: 'overdue' as const },
  {
    id: 3,
    name: 'Kennel Cough',
    date: '2025-04-19',
    status: 'due-soon' as const,
  },
];

export const initialTreatment: Array<TreatmentRecord> = [
  { id: 1, name: '', date: null, status: 'up-to-date' },
];
